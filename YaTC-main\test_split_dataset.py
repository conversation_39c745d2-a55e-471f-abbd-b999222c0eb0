#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的split_dataset.py功能
验证每流一PNG模式的数据集划分
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_dataset():
    """创建测试数据集，包含流文件和原始文件"""
    # 创建临时目录
    test_dir = Path(tempfile.mkdtemp(prefix="test_split_dataset_"))
    
    # 创建类别目录
    classes = ['OpenVPN', 'Wireguard', 'SSLVPN']
    
    for class_name in classes:
        class_dir = test_dir / class_name
        class_dir.mkdir()
        
        # 创建一些测试PNG文件
        # 模拟原始文件
        for i in range(3):
            original_file = class_dir / f"original_{i+1}.png"
            original_file.touch()
        
        # 模拟流文件（每流一PNG模式生成的文件）
        for i in range(5):  # 5个源文件
            for j in range(2, 6):  # 每个源文件产生2-5个流
                flow_file = class_dir / f"sample_{i+1}_flow_{j:03d}.png"
                flow_file.touch()
    
    print(f"测试数据集创建在: {test_dir}")
    return test_dir

def test_flow_file_detection():
    """测试流文件检测功能"""
    print("\n=== 测试流文件检测功能 ===")
    
    try:
        from split_dataset import is_flow_file
        
        # 测试各种文件名
        test_files = [
            "sample1_flow_001.png",  # 流文件
            "sample2_flow_010.png",  # 流文件
            "original_file.png",     # 原始文件
            "data_aug_1.png",        # 增强文件
            "test_flow_abc.png",     # 无效流文件
        ]
        
        for filename in test_files:
            is_flow, base_name, flow_num = is_flow_file(filename)
            print(f"文件: {filename}")
            print(f"  是否为流文件: {is_flow}")
            print(f"  基础名称: {base_name}")
            print(f"  流序号: {flow_num}")
            print()
        
        return True
        
    except Exception as e:
        print(f"流文件检测测试失败: {e}")
        return False

def test_file_grouping():
    """测试文件分组功能"""
    print("\n=== 测试文件分组功能 ===")
    
    try:
        from split_dataset import group_files_by_source, analyze_file_groups
        
        # 创建测试数据集
        test_dir = create_test_dataset()
        
        try:
            # 测试单个类别目录
            test_class_dir = test_dir / 'OpenVPN'
            files = list(test_class_dir.glob('*.png'))
            
            print(f"测试目录: {test_class_dir}")
            print(f"总文件数: {len(files)}")
            
            # 测试流模式分组
            print("\n流模式分组:")
            groups_flow = group_files_by_source(files, enable_flow_mode=True)
            analysis_flow = analyze_file_groups(files, enable_flow_mode=True)
            
            print(f"  组数: {len(groups_flow)}")
            print(f"  流文件: {analysis_flow['flow_files']}")
            print(f"  原始文件: {analysis_flow['original_files']}")
            print(f"  平均每组文件数: {analysis_flow['avg_files_per_group']:.1f}")
            
            # 显示前几个组的详情
            for i, (group_name, group_files) in enumerate(list(groups_flow.items())[:3]):
                print(f"  组 '{group_name}': {len(group_files)} 个文件")
            
            # 测试传统模式分组
            print("\n传统模式分组:")
            groups_traditional = group_files_by_source(files, enable_flow_mode=False)
            analysis_traditional = analyze_file_groups(files, enable_flow_mode=False)
            
            print(f"  组数: {len(groups_traditional)}")
            print(f"  平均每组文件数: {analysis_traditional['avg_files_per_group']:.1f}")
            
            return True
            
        finally:
            # 清理测试数据
            shutil.rmtree(test_dir)
        
    except Exception as e:
        print(f"文件分组测试失败: {e}")
        return False

def test_dataset_splitting():
    """测试数据集划分功能"""
    print("\n=== 测试数据集划分功能 ===")
    
    try:
        from split_dataset import split_dataset_vpn, validate_split_results
        
        # 创建测试数据集
        test_dir = create_test_dataset()
        
        try:
            print(f"测试数据集目录: {test_dir}")
            
            # 测试流模式划分
            print("\n测试流模式划分:")
            success = split_dataset_vpn(
                base_dir=test_dir,
                train_ratio=0.6,
                val_ratio=0.2,
                test_ratio=0.2,
                copy_files=True,
                enable_flow_mode=True
            )
            
            if success:
                print("✓ 流模式划分成功")
                
                # 验证结果
                print("\n验证划分结果:")
                validate_split_results(test_dir, enable_flow_mode=True)
            else:
                print("✗ 流模式划分失败")
                return False
            
            return True
            
        finally:
            # 清理测试数据
            shutil.rmtree(test_dir)
        
    except Exception as e:
        print(f"数据集划分测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("split_dataset.py 功能测试（每流一PNG模式）")
    print("=" * 60)
    
    tests = [
        ("流文件检测", test_flow_file_detection),
        ("文件分组", test_file_grouping),
        ("数据集划分", test_dataset_splitting),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"{'✓' if result else '✗'} {test_name} 测试{'通过' if result else '失败'}")
        except Exception as e:
            results.append((test_name, False))
            print(f"✗ {test_name} 测试异常: {e}")
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试总结:")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！split_dataset.py 已成功适配每流一PNG模式")
    else:
        print("⚠️  部分测试失败，请检查代码")

if __name__ == "__main__":
    main()
