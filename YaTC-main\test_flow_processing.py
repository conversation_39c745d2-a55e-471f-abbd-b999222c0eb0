#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试流处理功能的简单脚本（不依赖外部模块）
"""

import sys
import os

# 简单的配置测试，不导入完整的data_process模块
def test_basic_config():
    """测试基本配置"""
    print("测试基本配置...")

    # 模拟配置参数
    MAX_PACKETS_PER_FLOW = 5
    ENABLE_FLOW_GROUPING = True
    ENABLE_PER_FLOW_PNG = True
    MAX_PCAP_FILE_SIZE_GB = 1.0
    MIN_PACKETS_PER_FLOW = 1

    print(f"每流最大包数: {MAX_PACKETS_PER_FLOW}")
    print(f"流分组功能: {'启用' if ENABLE_FLOW_GROUPING else '禁用'}")
    print(f"每流一PNG模式: {'启用' if ENABLE_PER_FLOW_PNG else '禁用'}")
    print(f"文件大小限制: {MAX_PCAP_FILE_SIZE_GB}GB")
    print(f"最小流包数: {MIN_PACKETS_PER_FLOW}")

    # 验证配置合理性
    if MAX_PACKETS_PER_FLOW < 1:
        print("警告: MAX_PACKETS_PER_FLOW 不能小于1")
        return False
    elif MAX_PACKETS_PER_FLOW > 20:
        print("警告: MAX_PACKETS_PER_FLOW 过大可能影响性能")

    if MAX_PCAP_FILE_SIZE_GB <= 0:
        print("警告: MAX_PCAP_FILE_SIZE_GB 必须大于0")
        return False

    print("配置验证通过")
    return True

def test_file_structure():
    """测试文件结构"""
    print("测试文件结构...")

    # 检查data_process.py是否存在
    data_process_path = "data_process.py"
    if os.path.exists(data_process_path):
        print(f"✓ 找到 {data_process_path}")

        # 检查文件内容是否包含新增的函数
        with open(data_process_path, 'r', encoding='utf-8') as f:
            content = f.read()

        functions_to_check = [
            'identify_flows',
            'extract_flow_features',
            'read_flows_list_advanced',
            'validate_flow_config',
            'check_pcap_file_size',
            'process_pcap_flows_to_pngs',
            'process_pcap_flows_worker'
        ]

        for func_name in functions_to_check:
            if f"def {func_name}" in content:
                print(f"✓ 找到函数: {func_name}")
            else:
                print(f"✗ 未找到函数: {func_name}")

        # 检查配置参数
        config_params = [
            'MAX_PACKETS_PER_FLOW',
            'ENABLE_FLOW_GROUPING',
            'FLOW_TIMEOUT',
            'ENABLE_PER_FLOW_PNG',
            'MAX_PCAP_FILE_SIZE_GB',
            'MIN_PACKETS_PER_FLOW'
        ]

        for param in config_params:
            if param in content:
                print(f"✓ 找到配置参数: {param}")
            else:
                print(f"✗ 未找到配置参数: {param}")

    else:
        print(f"✗ 未找到 {data_process_path}")

    print()

def main():
    """主测试函数"""
    print("=" * 50)
    print("流处理功能测试")
    print("=" * 50)

    test_basic_config()
    print()
    test_file_structure()

    print("=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
