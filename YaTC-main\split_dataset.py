import os
import glob
import shutil
import random
from pathlib import Path
from tqdm import tqdm
import logging
from collections import defaultdict
import re

def is_flow_file(filename):
    """
    判断文件是否为流生成的PNG文件

    Args:
        filename: 文件名

    Returns:
        tuple: (是否为流文件, 原始文件名, 流序号)
    """
    if not filename.endswith('.png'):
        return False, None, None

    # 检查是否符合流文件命名格式：{原文件名}_flow_{流序号}.png
    if '_flow_' in filename:
        parts = filename.rsplit('_flow_', 1)
        if len(parts) == 2:
            base_name = parts[0]
            flow_part = parts[1].replace('.png', '')
            try:
                flow_number = int(flow_part)
                return True, base_name, flow_number
            except ValueError:
                pass

    # 如果不是流文件格式，返回原始文件信息
    base_name = filename.replace('.png', '')
    return False, base_name, None

def group_files_by_source(files, enable_flow_mode=True):
    """
    将文件按源文件分组

    Args:
        files: 文件路径列表
        enable_flow_mode: 是否启用流感知模式

    Returns:
        dict: 源文件名到文件列表的映射
    """
    if not enable_flow_mode:
        # 传统模式：每个文件作为独立组
        return {f.stem: [f] for f in files}

    groups = defaultdict(list)

    for file_path in files:
        is_flow, base_name, flow_num = is_flow_file(file_path.name)

        if is_flow:
            # 流文件：按原始文件名分组
            groups[base_name].append(file_path)
        else:
            # 原始文件：使用文件名作为组名
            groups[file_path.stem].append(file_path)

    return dict(groups)

def analyze_file_groups(files, enable_flow_mode=True):
    """
    分析文件分组情况

    Args:
        files: 文件路径列表
        enable_flow_mode: 是否启用流感知模式

    Returns:
        dict: 包含分析结果的字典
    """
    groups = group_files_by_source(files, enable_flow_mode)

    flow_files = []
    original_files = []

    for file_path in files:
        is_flow, _, _ = is_flow_file(file_path.name)
        if is_flow:
            flow_files.append(file_path)
        else:
            original_files.append(file_path)

    return {
        'total_files': len(files),
        'flow_files': len(flow_files),
        'original_files': len(original_files),
        'groups': groups,
        'group_count': len(groups),
        'avg_files_per_group': len(files) / max(len(groups), 1)
    }

def split_dataset_vpn(base_dir, train_ratio=0.7, val_ratio=0.15, test_ratio=0.15, copy_files=True, enable_flow_mode=True):
    """
    将VPN通道数据集的MFR图像划分为训练集、验证集和测试集（支持每流一PNG模式）

    Args:
        base_dir: 包含各类别MFR图像的基础目录
        train_ratio: 训练集比例 (默认70%)
        val_ratio: 验证集比例 (默认15%)
        test_ratio: 测试集比例 (默认15%)
        copy_files: 是否复制文件而非移动 (默认True，保留原文件)
        enable_flow_mode: 是否启用流感知划分模式 (默认True)

    流感知模式说明:
        - 启用时：来自同一原始pcap文件的所有流文件会被分配到同一个数据集分割中
        - 禁用时：使用传统的随机文件划分方式
        - 流文件命名格式：{原文件名}_flow_{流序号}.png

    输入目录结构:
    base_dir/
    ├── OpenVPN_Vless/
    │   ├── sample1_flow_001.png  # 流文件
    │   ├── sample1_flow_002.png  # 同一源的流文件
    │   ├── sample2_flow_001.png  # 另一源的流文件
    │   └── original.png          # 原始文件
    └── Vmess/
        ├── data1_flow_001.png
        └── data1_flow_002.png

    输出目录结构:
    base_dir/
    ├── train/
    │   ├── OpenVPN_Vless/
    │   └── Vmess/
    ├── val/
    │   ├── OpenVPN_Vless/
    │   └── Vmess/
    └── test/
        ├── OpenVPN_Vless/
        └── Vmess/
    """
    # 验证比例总和
    if abs(train_ratio + val_ratio + test_ratio - 1.0) > 0.001:
        raise ValueError(f"比例总和必须为1.0，当前为: {train_ratio + val_ratio + test_ratio}")

    base_path = Path(base_dir)

    # 查找类别目录（排除已存在的划分目录）
    source_dirs = [d for d in base_path.iterdir()
                   if d.is_dir() and d.name not in ['train', 'val', 'test']]

    if not source_dirs:
        print(f"错误: 在 '{base_dir}' 中未找到类别目录")
        print("脚本期望在基础目录中找到各类别的子目录")
        return False

    # 创建输出目录
    train_path = base_path / 'train'
    val_path = base_path / 'val'
    test_path = base_path / 'test'

    print(f"创建训练集目录: {train_path}")
    train_path.mkdir(exist_ok=True)
    print(f"创建验证集目录: {val_path}")
    val_path.mkdir(exist_ok=True)
    print(f"创建测试集目录: {test_path}")
    test_path.mkdir(exist_ok=True)

    # 统计信息
    total_files = 0
    total_groups = 0
    class_stats = {}
    class_flow_stats = {}

    print(f"\n发现 {len(source_dirs)} 个类别目录:")
    print(f"流感知模式: {'启用' if enable_flow_mode else '禁用'}")

    for class_dir in source_dirs:
        files = list(class_dir.glob('*.png'))
        analysis = analyze_file_groups(files, enable_flow_mode)

        class_stats[class_dir.name] = analysis['total_files']
        class_flow_stats[class_dir.name] = analysis
        total_files += analysis['total_files']
        total_groups += analysis['group_count']

        if enable_flow_mode:
            print(f"  {class_dir.name}: {analysis['total_files']} 个文件 "
                  f"(流文件: {analysis['flow_files']}, 原始: {analysis['original_files']}, "
                  f"组: {analysis['group_count']}, 平均: {analysis['avg_files_per_group']:.1f}/组)")
        else:
            print(f"  {class_dir.name}: {analysis['total_files']} 个文件")

    print(f"\n总文件数: {total_files}")
    if enable_flow_mode:
        print(f"总组数: {total_groups}")
        print(f"平均每组文件数: {total_files / max(total_groups, 1):.1f}")
    print(f"划分比例: 训练集 {train_ratio*100:.1f}%, 验证集 {val_ratio*100:.1f}%, 测试集 {test_ratio*100:.1f}%")

    # 处理各类别目录
    print("\n开始处理类别目录...")
    split_stats = {'train': 0, 'val': 0, 'test': 0}

    for class_dir in tqdm(source_dirs, desc="处理类别"):
        class_name = class_dir.name

        # 创建各子集的类别目录
        train_class_path = train_path / class_name
        train_class_path.mkdir(exist_ok=True)

        val_class_path = val_path / class_name
        val_class_path.mkdir(exist_ok=True)

        test_class_path = test_path / class_name
        test_class_path.mkdir(exist_ok=True)

        # 获取所有PNG文件
        files = list(class_dir.glob('*.png'))
        if not files:
            print(f"警告: 类别 {class_name} 中没有PNG文件")
            continue

        if enable_flow_mode:
            # 流感知模式：按组划分
            file_groups = group_files_by_source(files, enable_flow_mode)
            group_names = list(file_groups.keys())
            random.shuffle(group_names)  # 随机打乱组的顺序

            # 计算组的划分索引
            train_group_split = int(len(group_names) * train_ratio)
            val_group_split = int(len(group_names) * (train_ratio + val_ratio))

            train_groups = group_names[:train_group_split]
            val_groups = group_names[train_group_split:val_group_split]
            test_groups = group_names[val_group_split:]

            # 收集各组的所有文件
            train_files = []
            val_files = []
            test_files = []

            for group_name in train_groups:
                train_files.extend(file_groups[group_name])
            for group_name in val_groups:
                val_files.extend(file_groups[group_name])
            for group_name in test_groups:
                test_files.extend(file_groups[group_name])

        else:
            # 传统模式：随机文件划分
            random.shuffle(files)

            # 计算划分索引
            train_split = int(len(files) * train_ratio)
            val_split = int(len(files) * (train_ratio + val_ratio))

            train_files = files[:train_split]
            val_files = files[train_split:val_split]
            test_files = files[val_split:]

        # 更新统计
        split_stats['train'] += len(train_files)
        split_stats['val'] += len(val_files)
        split_stats['test'] += len(test_files)

        # 复制或移动文件
        operation = shutil.copy2 if copy_files else shutil.move
        operation_name = "复制" if copy_files else "移动"

        # 处理训练集文件
        for file_path in tqdm(train_files, desc=f'{operation_name} {class_name} (训练集)', leave=False):
            operation(str(file_path), str(train_class_path / file_path.name))

        # 处理验证集文件
        for file_path in tqdm(val_files, desc=f'{operation_name} {class_name} (验证集)', leave=False):
            operation(str(file_path), str(val_class_path / file_path.name))

        # 处理测试集文件
        for file_path in tqdm(test_files, desc=f'{operation_name} {class_name} (测试集)', leave=False):
            operation(str(file_path), str(test_class_path / file_path.name))

    # 输出最终统计
    print(f"\n数据集划分完成!")
    print(f"训练集: {split_stats['train']} 个文件 ({split_stats['train']/total_files*100:.1f}%)")
    print(f"验证集: {split_stats['val']} 个文件 ({split_stats['val']/total_files*100:.1f}%)")
    print(f"测试集: {split_stats['test']} 个文件 ({split_stats['test']/total_files*100:.1f}%)")

    # 如果是移动文件，清理空的源目录
    if not copy_files:
        print("\n清理空的源目录...")
        for class_dir in source_dirs:
            try:
                class_dir.rmdir()
                print(f"已删除空目录: {class_dir}")
            except OSError:
                print(f"目录非空，保留: {class_dir}")

    return True


def validate_split_results(base_dir, enable_flow_mode=True):
    """验证数据集划分结果（支持流统计）"""
    base_path = Path(base_dir)

    train_path = base_path / 'train'
    val_path = base_path / 'val'
    test_path = base_path / 'test'

    if not all([train_path.exists(), val_path.exists(), test_path.exists()]):
        print("错误: 划分目录不完整")
        return False

    print(f"\n=== 数据集划分验证{'（流感知模式）' if enable_flow_mode else ''} ===")

    # 获取所有类别
    train_classes = set(d.name for d in train_path.iterdir() if d.is_dir())
    val_classes = set(d.name for d in val_path.iterdir() if d.is_dir())
    test_classes = set(d.name for d in test_path.iterdir() if d.is_dir())

    all_classes = train_classes | val_classes | test_classes

    print(f"发现 {len(all_classes)} 个类别: {sorted(all_classes)}")
    print(f"流感知模式: {'启用' if enable_flow_mode else '禁用'}")

    # 检查类别一致性
    if train_classes == val_classes == test_classes:
        print("✓ 所有子集包含相同的类别")
    else:
        print("⚠ 警告: 子集间类别不一致")
        print(f"  训练集类别: {sorted(train_classes)}")
        print(f"  验证集类别: {sorted(val_classes)}")
        print(f"  测试集类别: {sorted(test_classes)}")

    # 统计各子集文件数量
    total_stats = {'train': 0, 'val': 0, 'test': 0}
    class_distribution = {}

    for class_name in sorted(all_classes):
        # 获取各子集的文件
        train_files = list((train_path / class_name).glob('*.png')) if (train_path / class_name).exists() else []
        val_files = list((val_path / class_name).glob('*.png')) if (val_path / class_name).exists() else []
        test_files = list((test_path / class_name).glob('*.png')) if (test_path / class_name).exists() else []

        train_count = len(train_files)
        val_count = len(val_files)
        test_count = len(test_files)
        total_count = train_count + val_count + test_count

        class_distribution[class_name] = {
            'train': train_count,
            'val': val_count,
            'test': test_count,
            'total': total_count
        }

        total_stats['train'] += train_count
        total_stats['val'] += val_count
        total_stats['test'] += test_count

        if total_count > 0:
            print(f"\n{class_name}:")

            if enable_flow_mode:
                # 分析流文件分布
                train_analysis = analyze_file_groups(train_files, enable_flow_mode)
                val_analysis = analyze_file_groups(val_files, enable_flow_mode)
                test_analysis = analyze_file_groups(test_files, enable_flow_mode)

                print(f"  训练集: {train_count:4d} 文件 ({train_count/total_count*100:5.1f}%) "
                      f"[流: {train_analysis['flow_files']}, 原始: {train_analysis['original_files']}, 组: {train_analysis['group_count']}]")
                print(f"  验证集: {val_count:4d} 文件 ({val_count/total_count*100:5.1f}%) "
                      f"[流: {val_analysis['flow_files']}, 原始: {val_analysis['original_files']}, 组: {val_analysis['group_count']}]")
                print(f"  测试集: {test_count:4d} 文件 ({test_count/total_count*100:5.1f}%) "
                      f"[流: {test_analysis['flow_files']}, 原始: {test_analysis['original_files']}, 组: {test_analysis['group_count']}]")
                print(f"  总计:   {total_count:4d} 文件")
            else:
                print(f"  训练集: {train_count:4d} ({train_count/total_count*100:5.1f}%)")
                print(f"  验证集: {val_count:4d} ({val_count/total_count*100:5.1f}%)")
                print(f"  测试集: {test_count:4d} ({test_count/total_count*100:5.1f}%)")
                print(f"  总计:   {total_count:4d}")

    # 总体统计
    total_files = sum(total_stats.values())
    print(f"\n=== 总体统计 ===")
    print(f"训练集: {total_stats['train']:4d} 个文件 ({total_stats['train']/total_files*100:5.1f}%)")
    print(f"验证集: {total_stats['val']:4d} 个文件 ({total_stats['val']/total_files*100:5.1f}%)")
    print(f"测试集: {total_stats['test']:4d} 个文件 ({total_stats['test']/total_files*100:5.1f}%)")
    print(f"总计:   {total_files:4d} 个文件")

    # 如果启用流模式，验证流的完整性
    if enable_flow_mode:
        print(f"\n=== 流完整性验证 ===")
        validate_flow_integrity(base_dir)

    return True

def validate_flow_integrity(base_dir):
    """验证流文件的完整性，确保同一源的流文件在同一个数据集分割中"""
    base_path = Path(base_dir)

    splits = ['train', 'val', 'test']
    integrity_issues = []

    # 收集所有类别
    all_classes = set()
    for split in splits:
        split_path = base_path / split
        if split_path.exists():
            all_classes.update(d.name for d in split_path.iterdir() if d.is_dir())

    for class_name in sorted(all_classes):
        # 收集每个分割中的流组
        split_groups = {}

        for split in splits:
            class_path = base_path / split / class_name
            if class_path.exists():
                files = list(class_path.glob('*.png'))
                groups = group_files_by_source(files, enable_flow_mode=True)
                split_groups[split] = set(groups.keys())

        # 检查是否有流组跨越多个分割
        all_groups = set()
        for split, groups in split_groups.items():
            all_groups.update(groups)

        for group_name in all_groups:
            splits_containing_group = [split for split, groups in split_groups.items() if group_name in groups]

            if len(splits_containing_group) > 1:
                integrity_issues.append({
                    'class': class_name,
                    'group': group_name,
                    'splits': splits_containing_group
                })

    if integrity_issues:
        print("⚠️  发现流完整性问题:")
        for issue in integrity_issues:
            print(f"  类别 {issue['class']}, 组 {issue['group']} 出现在: {issue['splits']}")
        print("这可能导致数据泄露！")
    else:
        print("✓ 流完整性验证通过，没有发现跨分割的流组")

if __name__ == '__main__':
    # ==================== VPN数据集划分配置 ====================

    # VPN通道数据集MFR图像目录
    DATASET_BASE_DIR = 'D:/VPN通道识别/YaTC-main/data/VPN_MFR'

    # 数据集划分比例
    TRAIN_RATIO = 0.7   # 70% 训练集
    VAL_RATIO = 0.15    # 15% 验证集
    TEST_RATIO = 0.15   # 15% 测试集

    # 是否复制文件（True=保留原文件，False=移动文件）
    COPY_FILES = True

    # 是否启用流感知划分模式（True=按流组划分，False=传统随机划分）
    ENABLE_FLOW_MODE = True

    # ==========================================================

    print("=" * 60)
    print("YaTC VPN通道数据集划分工具（支持每流一PNG模式）")
    print("=" * 60)
    print(f"源目录: {DATASET_BASE_DIR}")
    print(f"划分比例: 训练集 {TRAIN_RATIO*100:.0f}% | 验证集 {VAL_RATIO*100:.0f}% | 测试集 {TEST_RATIO*100:.0f}%")
    print(f"文件操作: {'复制' if COPY_FILES else '移动'}")
    print(f"流感知模式: {'启用' if ENABLE_FLOW_MODE else '禁用'}")
    if ENABLE_FLOW_MODE:
        print("  - 同一源文件的流将被分配到同一数据集分割中")
        print("  - 避免数据泄露，确保流的完整性")
    print("=" * 60)

    # 检查源目录是否存在
    if not os.path.exists(DATASET_BASE_DIR):
        print(f"错误: 源目录不存在 - {DATASET_BASE_DIR}")
        print("请确保MFR图像生成已完成")
        exit(1)

    try:
        # 执行数据集划分
        success = split_dataset_vpn(
            DATASET_BASE_DIR,
            train_ratio=TRAIN_RATIO,
            val_ratio=VAL_RATIO,
            test_ratio=TEST_RATIO,
            copy_files=COPY_FILES,
            enable_flow_mode=ENABLE_FLOW_MODE
        )

        if success:
            # 验证划分结果
            validate_split_results(DATASET_BASE_DIR, ENABLE_FLOW_MODE)
            print("\n" + "=" * 60)
            print("数据集划分完成!")
            print("=" * 60)
        else:
            print("数据集划分失败!")

    except Exception as e:
        print(f"划分过程中发生错误: {e}")
        import traceback
        traceback.print_exc()