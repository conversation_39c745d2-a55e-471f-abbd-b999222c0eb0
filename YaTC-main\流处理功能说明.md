# 流处理功能说明

## 概述

本次修改为 `data_process.py` 添加了网络流识别和分组处理功能，实现了对每个网络流只提取前N个数据包进行处理的需求。

## 主要功能

### 1. 流识别和分组
- 基于五元组（源IP、目标IP、源端口、目标端口、协议）识别网络流
- 自动将双向通信识别为同一个流
- 支持TCP和UDP协议的流识别

### 2. 可配置的包数量限制
- `MAX_PACKETS_PER_FLOW`: 每个流处理的最大包数量（默认5）
- 可在代码顶部轻松修改配置

### 3. 多种处理模式
- **流分组模式**（推荐）: 识别网络流，对每个流只处理前N个包
- **原始模式**: 处理文件中的前N个包，不进行流分组
- **高级流处理**: 处理多个流，从每个流中提取包（实验性功能）

## 配置参数

```python
# 每个流处理的最大包数量（可配置）
MAX_PACKETS_PER_FLOW = 5

# 流识别配置
FLOW_TIMEOUT = 60  # 流超时时间（秒）
ENABLE_FLOW_GROUPING = True  # 是否启用流分组功能

# 在主函数中的配置
use_advanced_flow_processing = False  # 是否使用高级流处理
```

## 新增函数

### `identify_flows(packets)`
识别网络流，基于五元组进行分组。

**参数:**
- `packets`: scapy数据包列表

**返回:**
- `dict`: 流ID到数据包列表的映射

### `extract_flow_features(flow_packets, max_packets=MAX_PACKETS_PER_FLOW)`
从单个流的数据包中提取前N个包的特征。

**参数:**
- `flow_packets`: 属于同一流的数据包列表
- `max_packets`: 最大处理包数量

**返回:**
- `list`: 包含(header, payload)元组的列表

### `read_flows_list_advanced(pcap_dir, max_flows=3)`
高级流处理：从pcap文件中识别多个流，并从每个流中提取前N个包。

### `validate_flow_config()`
验证流处理配置的合理性。

## 边界情况处理

1. **流中包数少于N个**: 使用该流的所有包，不足部分用零填充
2. **无法识别流**: 自动回退到原始处理模式
3. **文件读取失败**: 返回默认的零填充数据
4. **空文件或无效文件**: 返回零填充的默认数据

## 性能优化

1. **内存控制**: 限制读取的最大包数量以控制内存使用
2. **并行处理**: 支持多进程并行处理
3. **自动垃圾回收**: 定期清理内存
4. **快速失败**: 对无效文件快速跳过

## 使用建议

1. **对于VPN流量分析**: 建议启用流分组模式
2. **包数量设置**: `MAX_PACKETS_PER_FLOW` 设置为5-10较为合适
3. **大数据集处理**: 可禁用高级流处理以提高性能
4. **内存受限环境**: 可适当减少 `MAX_PACKETS_PER_FLOW` 的值

## 兼容性

- 保持与原有代码的完全兼容性
- 如果禁用流分组功能，将回退到原始处理逻辑
- 所有原有的错误处理和内存优化机制都得到保留

## 测试

运行 `test_flow_processing.py` 可以验证功能是否正常工作：

```bash
python test_flow_processing.py
```

## 日志输出

程序运行时会显示当前的流处理配置：

```
流处理配置:
  流分组功能: 启用
  每流最大包数: 5
  高级流处理: 禁用
```

这样用户可以清楚地了解当前使用的处理模式。
