# 流处理功能说明（增强版）

## 概述

本次重大更新为 `data_process.py` 添加了**每流一PNG模式**，实现了从"每个pcap文件生成一个PNG"到"每个网络流生成一个PNG"的核心功能转换。这将大大增加训练样本数量，提高模型的泛化能力。

## 核心新功能：每流一PNG模式

### 🎯 核心改进
**从"每个pcap文件生成一个PNG"改为"每个网络流生成一个PNG"**

- **传统模式**: 1个pcap文件 → 1个PNG图像 → 1个训练样本
- **新模式**: 1个pcap文件 → N个网络流 → N个PNG图像 → N个训练样本

### 🚀 主要优势
1. **样本数量大幅增加**: 一个pcap文件可能包含多个流，每个流都成为独立样本
2. **更好的特征表示**: 每个PNG代表一个完整的网络流，特征更纯净
3. **提高模型泛化能力**: 更多样化的训练数据
4. **保持数据质量**: 每个流仍然提取前5个包，保持特征一致性

## 主要功能

### 1. 流识别和分组
- 基于五元组（源IP、目标IP、源端口、目标端口、协议）识别网络流
- 自动将双向通信识别为同一个流
- 支持TCP和UDP协议的流识别

### 2. 智能文件过滤
- 自动跳过超过1GB的大文件，避免内存问题
- 文件大小预检查，提高处理效率
- 支持自定义文件大小限制

### 3. 多种处理模式
- **每流一PNG模式**（新增，推荐）: 为每个网络流生成独立的PNG文件
- **传统模式**: 每个pcap文件生成一个PNG文件
- **流分组模式**: 识别网络流，对每个流只处理前N个包
- **高级流处理**: 处理多个流，从每个流中提取包（实验性功能）

## 配置参数

### 基础配置
```python
# 每个流处理的最大包数量（可配置）
MAX_PACKETS_PER_FLOW = 5

# 流识别配置
FLOW_TIMEOUT = 60  # 流超时时间（秒）
ENABLE_FLOW_GROUPING = True  # 是否启用流分组功能
```

### 新增：每流一PNG模式配置
```python
# 核心新功能开关
ENABLE_PER_FLOW_PNG = True  # 是否启用每流一PNG模式

# 文件处理限制
MAX_PCAP_FILE_SIZE_GB = 1.0  # pcap文件大小限制（GB）
MIN_PACKETS_PER_FLOW = 1     # 流的最小包数量

# 在主函数中的配置
use_advanced_flow_processing = False  # 是否使用高级流处理
```

## 新增函数

### `identify_flows(packets)`
识别网络流，基于五元组进行分组。

**参数:**
- `packets`: scapy数据包列表

**返回:**
- `dict`: 流ID到数据包列表的映射

### `extract_flow_features(flow_packets, max_packets=MAX_PACKETS_PER_FLOW)`
从单个流的数据包中提取前N个包的特征。

**参数:**
- `flow_packets`: 属于同一流的数据包列表
- `max_packets`: 最大处理包数量

**返回:**
- `list`: 包含(header, payload)元组的列表

### `read_flows_list_advanced(pcap_dir, max_flows=3)`
高级流处理：从pcap文件中识别多个流，并从每个流中提取前N个包。

### `validate_flow_config()`
验证流处理配置的合理性。

## 边界情况处理

1. **流中包数少于N个**: 使用该流的所有包，不足部分用零填充
2. **无法识别流**: 自动回退到原始处理模式
3. **文件读取失败**: 返回默认的零填充数据
4. **空文件或无效文件**: 返回零填充的默认数据

## 性能优化

1. **内存控制**: 限制读取的最大包数量以控制内存使用
2. **并行处理**: 支持多进程并行处理
3. **自动垃圾回收**: 定期清理内存
4. **快速失败**: 对无效文件快速跳过

## 使用建议

1. **对于VPN流量分析**: 建议启用流分组模式
2. **包数量设置**: `MAX_PACKETS_PER_FLOW` 设置为5-10较为合适
3. **大数据集处理**: 可禁用高级流处理以提高性能
4. **内存受限环境**: 可适当减少 `MAX_PACKETS_PER_FLOW` 的值

## 兼容性

- 保持与原有代码的完全兼容性
- 如果禁用流分组功能，将回退到原始处理逻辑
- 所有原有的错误处理和内存优化机制都得到保留

## 测试

运行 `test_flow_processing.py` 可以验证功能是否正常工作：

```bash
python test_flow_processing.py
```

## 日志输出

程序运行时会显示当前的流处理配置：

```
流处理配置:
  流分组功能: 启用
  每流最大包数: 5
  高级流处理: 禁用
```

这样用户可以清楚地了解当前使用的处理模式。
