#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的data_preprocessing.py功能
验证每流一PNG模式的支持
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_dataset():
    """创建测试数据集，包含流文件和原始文件"""
    # 创建临时目录
    test_dir = Path(tempfile.mkdtemp(prefix="test_vpn_dataset_"))
    
    # 创建数据集结构
    splits = ['train', 'val', 'test']
    classes = ['OpenVPN', 'Wireguard', 'SSLVPN']
    
    for split in splits:
        split_dir = test_dir / split
        split_dir.mkdir()
        
        for class_name in classes:
            class_dir = split_dir / class_name
            class_dir.mkdir()
            
            # 创建一些测试PNG文件
            # 模拟原始文件
            for i in range(2):
                original_file = class_dir / f"original_{i+1}.png"
                original_file.touch()
            
            # 模拟流文件（每流一PNG模式生成的文件）
            for i in range(3):
                for j in range(2, 5):  # 每个原始文件产生2-4个流
                    flow_file = class_dir / f"sample_{i+1}_flow_{j:03d}.png"
                    flow_file.touch()
    
    print(f"测试数据集创建在: {test_dir}")
    return test_dir

def test_flow_file_detection():
    """测试流文件检测功能"""
    print("\n=== 测试流文件检测功能 ===")

    try:
        # 直接测试流文件检测逻辑，不导入完整模块
        def is_flow_file(filename):
            """简化版流文件检测"""
            if not filename.endswith('.png'):
                return False, None, None

            # 检查是否符合流文件命名格式：{原文件名}_flow_{流序号}.png
            if '_flow_' in filename:
                parts = filename.rsplit('_flow_', 1)
                if len(parts) == 2:
                    base_name = parts[0]
                    flow_part = parts[1].replace('.png', '')
                    try:
                        flow_number = int(flow_part)
                        return True, base_name, flow_number
                    except ValueError:
                        pass

            # 如果不是流文件格式，返回原始文件信息
            base_name = filename.replace('.png', '')
            return False, base_name, None

        # 测试各种文件名
        test_files = [
            "sample1_flow_001.png",  # 流文件
            "sample2_flow_010.png",  # 流文件
            "original_file.png",     # 原始文件
            "data_aug_1.png",        # 增强文件
            "test_flow_abc.png",     # 无效流文件
        ]

        for filename in test_files:
            is_flow, base_name, flow_num = is_flow_file(filename)
            print(f"文件: {filename}")
            print(f"  是否为流文件: {is_flow}")
            print(f"  基础名称: {base_name}")
            print(f"  流序号: {flow_num}")
            print()

        return True

    except Exception as e:
        print(f"流文件检测测试失败: {e}")
        return False

def test_dataset_analysis():
    """测试数据集分析功能"""
    print("\n=== 测试数据集分析功能 ===")

    try:
        # 创建测试数据集
        test_dir = create_test_dataset()

        try:
            print("测试数据集结构创建:")
            print(f"测试目录: {test_dir}")

            # 检查创建的文件
            for split in ['train', 'val', 'test']:
                split_dir = test_dir / split
                if split_dir.exists():
                    print(f"  {split} 目录存在")
                    for class_dir in split_dir.iterdir():
                        if class_dir.is_dir():
                            files = list(class_dir.glob('*.png'))
                            flow_files = [f for f in files if '_flow_' in f.name]
                            original_files = [f for f in files if '_flow_' not in f.name]
                            print(f"    {class_dir.name}: 总文件 {len(files)}, 流文件 {len(flow_files)}, 原始文件 {len(original_files)}")

            return True

        finally:
            # 清理测试数据
            shutil.rmtree(test_dir)

    except Exception as e:
        print(f"数据集分析测试失败: {e}")
        return False

def test_file_info_extraction():
    """测试文件信息提取功能"""
    print("\n=== 测试文件信息提取功能 ===")

    try:
        # 创建测试数据集
        test_dir = create_test_dataset()

        try:
            # 简化版文件信息提取测试
            test_class_dir = test_dir / 'train' / 'OpenVPN'
            png_files = list(test_class_dir.glob('*.png'))

            flow_files = []
            original_files = []
            flow_groups = {}

            for png_file in png_files:
                if '_flow_' in png_file.name:
                    flow_files.append(png_file)
                    # 提取基础名称
                    parts = png_file.name.rsplit('_flow_', 1)
                    if len(parts) == 2:
                        base_name = parts[0]
                        if base_name not in flow_groups:
                            flow_groups[base_name] = []
                        flow_groups[base_name].append(png_file)
                else:
                    original_files.append(png_file)

            print("文件信息提取结果:")
            print(f"  总文件数: {len(png_files)}")
            print(f"  流文件数: {len(flow_files)}")
            print(f"  原始文件数: {len(original_files)}")
            print(f"  流组数: {len(flow_groups)}")
            print(f"  流组详情: {[(k, len(v)) for k, v in flow_groups.items()]}")

            return True

        finally:
            # 清理测试数据
            shutil.rmtree(test_dir)

    except Exception as e:
        print(f"文件信息提取测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("data_preprocessing.py 功能测试（每流一PNG模式）")
    print("=" * 60)
    
    tests = [
        ("流文件检测", test_flow_file_detection),
        ("数据集分析", test_dataset_analysis),
        ("文件信息提取", test_file_info_extraction),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"✓ {test_name} 测试{'通过' if result else '失败'}")
        except Exception as e:
            results.append((test_name, False))
            print(f"✗ {test_name} 测试异常: {e}")
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试总结:")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！data_preprocessing.py 已成功适配每流一PNG模式")
    else:
        print("⚠️  部分测试失败，请检查代码")

if __name__ == "__main__":
    main()
